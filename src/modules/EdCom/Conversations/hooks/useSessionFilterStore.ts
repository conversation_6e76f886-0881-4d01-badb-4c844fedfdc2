import { useCallback, useEffect, useState } from 'react';
import { isEqual } from 'lodash';

/**
 * Session-based filter store that resets to default values on each new session (login).
 * Unlike useFilterStore which persists filters in the database, this hook stores filters
 * in sessionStorage which gets cleared when the user logs out and logs back in.
 */
export default function useSessionFilterStore<T extends {}>(
  filterKey: string,
  defaultValues: T,
): {
  values: T;
  onChange: (values: T) => void;
  loading: boolean;
} {
  const [values, setValues] = useState<T>(defaultValues);
  const [loading, setLoading] = useState(true);

  // Load values from sessionStorage on mount
  useEffect(() => {
    try {
      const storedValues = sessionStorage.getItem(filterKey);
      if (storedValues) {
        const parsedValues = JSON.parse(storedValues);
        // Merge with default values to ensure all required properties exist
        const mergedValues = { ...defaultValues, ...parsedValues };
        setValues(mergedValues);
      } else {
        // No stored values, use defaults
        setValues(defaultValues);
      }
    } catch (error) {
      console.warn(`Failed to load filter values for ${filterKey}:`, error);
      setValues(defaultValues);
    } finally {
      setLoading(false);
    }
  }, [filterKey, defaultValues]);

  const onChange = useCallback(
    (newValues: T) => {
      setValues(newValues);
      try {
        sessionStorage.setItem(filterKey, JSON.stringify(newValues));
      } catch (error) {
        console.warn(`Failed to save filter values for ${filterKey}:`, error);
      }
    },
    [filterKey],
  );

  return {
    values,
    onChange,
    loading,
  };
}
